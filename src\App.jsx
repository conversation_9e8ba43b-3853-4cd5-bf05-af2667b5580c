import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import Navigation from './components/Navigation';
import RegisterPage from './pages/RegisterPage';
import LoginPage from './pages/LoginPage';
import HomePage from './pages/HomePage';
import DetailPage from './pages/DetailPage';
import AddPage from './pages/AddPage';
import ArchivePage from './pages/ArchivePage';

// function App() {
//   return (
//     <div className="app-container">
//       <header>
//         <Navigation />
//       </header>
//       <main>
//         <Routes>
//           <Route path="/" element={<HomePage />} />
//           <Route path="/notes/:id" element={<DetailPage />} />
//           <Route path="/add" element={<AddPage />} />
//           <Route path="/archives" element={<ArchivePage />} />
//         </Routes>
//       </main>
//     </div>
//   );
// }

class App extends React.Component {
  constructor(props){
    super(props);

    this.state = {
      authedUser: null,
    }

    this.loginSuccessHandler = this.loginSuccessHandler.bind(this);
  }

  loginSuccessHandler(user) {
    this.setState({
      authedUser: user
    });
  }

  render(){
    if(this.state.authedUser === null){
      return (
        <div className='app-container'>
          <header>
            <h1>Aplikasi Catatan</h1>
          </header>
          <main>
            <Routes>
              <Route path="/login" element={<LoginPage loginSuccess={this.loginSuccessHandler} />} />
              <Route path="/register" element={<RegisterPage />} />
            </Routes>
          </main>
        </div>
      )
    }
    return (
      <div className="app-container">
         <header>
           <h1>Aplikasi Catatan</h1>
           <Navigation />
         </header>
         <main>
           <Routes>
             <Route path="/" element={<HomePage />} />
             <Route path="/notes/:id" element={<DetailPage />} />
             <Route path="/add" element={<AddPage />} />
             <Route path="/archives" element={<ArchivePage />} />
           </Routes>
         </main>
       </div>
    )
  }
}

export default App;
