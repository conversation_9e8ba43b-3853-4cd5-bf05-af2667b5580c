import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import Navigation from './components/Navigation';
import RegisterPage from './pages/RegisterPage';
import LoginPage from './pages/LoginPage';
import HomePage from './pages/HomePage';
import DetailPage from './pages/DetailPage';
import AddPage from './pages/AddPage';
import ArchivePage from './pages/ArchivePage';
import { ThemeProvider } from './contexts/ThemeContext';
import { getAccessToken, putAccessToken, getUserLogged } from './utils/network-data';
import ToggleTheme from './components/ToggleTheme';

// function App() {
//   return (
//     <div className="app-container">
//       <header>
//         <Navigation />
//       </header>
//       <main>
//         <Routes>
//           <Route path="/" element={<HomePage />} />
//           <Route path="/notes/:id" element={<DetailPage />} />
//           <Route path="/add" element={<AddPage />} />
//           <Route path="/archives" element={<ArchivePage />} />
//         </Routes>
//       </main>
//     </div>
//   );
// }

class App extends React.Component{
  constructor(props){
    super(props);

    this.state = {
      authedUser: null,
      initializing: true,
      themeContext: {
        theme: localStorage.getItem('theme') || 'dark',
        toggleTheme: () => {
          this.setState((prevState) => {
            const newTheme = prevState.themeContext.theme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('theme', newTheme);
            return {
              themeContext: {
                ...prevState.themeContext,
                theme: newTheme
              }
            }
          });
        }
      }
    };

    this.onLoginSuccess = this.onLoginSuccess.bind(this);
    this.onLogout = this.onLogout.bind(this);
  }

  async componentDidMount(){
    const accessToken = getAccessToken();

    if (accessToken) {
      const { error, data } = await getUserLogged();

      this.setState(()=>{
        return {
          authedUser: error ? null : data,
          initializing: false,
        };
      });
    } else {
      this.setState(()=>{
        return {
          initializing: false,
        };
      });
    }
  }

  async onLoginSuccess({ accessToken }){
    putAccessToken(accessToken);
    const { data } = await getUserLogged();

    this.setState(()=>{
      return {
        authedUser: data,
      };
    });
  }

  onLogout(){
    this.setState(()=>{
      return {
        authedUser: null,
      };
    });

    putAccessToken('');
  }
  
  render(){
    if(this.state.initializing){
      return null;
    }
    
    if(this.state.authedUser === null){
        return(
          <ThemeProvider>
            <div className='app-container'>
              <header>
                <h1>Aplikasi Catatan</h1>
              </header>
              <main>
                <Routes>
                  <Route path="/" element={<Navigate to="/login" replace />} />
                  <Route path="/login" element={<LoginPage loginSuccess={this.loginSuccessHandler} />} />
                  <Route path="/register" element={<RegisterPage />} />
                </Routes>
              </main>
            </div>
          </ThemeProvider>
        );
      }
    
      return (
        <ThemeProvider value={this.state.themeContext}>
          <div className='app-container'>
            <header>
              <ToggleTheme />
              <Navigation logout={this.onLogout} name={this.state.authedUser.name}/>
            </header>
            <main>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/notes/:id" element={<DetailPage />} />
                <Route path="/add" element={<AddPage />} />
                <Route path="/archives" element={<ArchivePage />} />
              </Routes>
            </main>
          </div>
        </ThemeProvider>
    );
  }
}


// class App extends React.Component {
//   constructor(props){
//     super(props);

//     this.state = {
//       authedUser: null,
//       initializing: true,
//       localeContext: {
//         locale: localStorage.getItem('locale') || 'id',
//         toggleLocale: () => {
//           this.setState((prevState) => {
//             const newLocale = prevState.localeContext.locale === 'id' ? 'en' : 'id';
//             localStorage.setItem('locale', newLocale);
//             return {
//               localeContext: {
//                 ...prevState.localeContext,
//                 locale: newLocale
//               }
//             }
//           });
//         }
//       }
//     };

//     this.onLoginSuccess = this.onLoginSuccess.bind(this);
//     this.onLogout = this.onLogout.bind(this);
//   }

//   async componentDidMount(){
//     const accessToken = getAccessToken();

//     if (accessToken) {
//       const { error, data } = await getUserLogged();

//       this.setState(()=>{
//         return {
//           authedUser: error ? null : data,
//           initializing: false,
//         };
//       });
//     } else {
//       this.setState(()=>{
//         return {
//           initializing: false,
//         };
//       });
//     }
//   }

//   async onLoginSuccess({ accessToken }){
//     putAccessToken(accessToken);
//     const { data } = await getUserLogged();

//     this.setState(()=>{
//       return {
//         authedUser: data,
//       };
//     });
//   }

//   onLogout(){
//     this.setState(()=>{
//       return {
//         authedUser: null,
//       };
//     });

//     putAccessToken('');
//   }
  

//   render(){
//     if(this.state.initializing){
//       return null;
//     }

//     if(this.state.authedUser === null){
//       return (
//         <div className='app-container'>
//           <header>
//             <h1>Aplikasi Catatan</h1>
//           </header>
//           <main>
//             <Routes>
//               <Route path="/" element={<Navigate to="/login" replace />} />
//               <Route path="/login" element={<LoginPage loginSuccess={this.loginSuccessHandler} />} />
//               <Route path="/register" element={<RegisterPage />} />
//             </Routes>
//           </main>
//         </div>
//       )
//     }
//     return (
//       <div className="app-container">
//          <header>
//            <h1>Aplikasi Catatan</h1>
//            <Navigation />
//          </header>
//          <main>
//            <Routes>
//              <Route path="/" element={<HomePage />} />
//              <Route path="/notes/:id" element={<DetailPage />} />
//              <Route path="/add" element={<AddPage />} />
//              <Route path="/archives" element={<ArchivePage />} />
//            </Routes>
//          </main>
//        </div>
//     )
//   }
// }

export default App;
