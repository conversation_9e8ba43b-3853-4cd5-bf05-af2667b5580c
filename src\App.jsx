import React from 'react';
import { Route, Routes } from 'react-router-dom';
import Navigation from './components/Navigation';
import HomePage from './pages/HomePage';
import DetailPage from './pages/DetailPage';
import AddPage from './pages/AddPage';
import ArchivePage from './pages/ArchivePage';

function App() {
  return (
    <div className="app-container">
      <h1>TEST - App is working!</h1>
      <header>
        <Navigation />
      </header>
      <main>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/notes/:id" element={<DetailPage />} />
          <Route path="/add" element={<AddPage />} />
          <Route path="/archives" element={<ArchivePage />} />
        </Routes>
      </main>
    </div>
  );
}

export default App;
