import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { getActiveNotes } from '../utils/local-data';
import NotesList from '../components/NotesList';
import SearchBar from '../components/SearchBar';

function HomePageWrapper(){
    const [searchParams, setSearchParams] = useSearchParams();
    const keyword = searchParams.get('keyword');

    function changeSearchParams(keyword){
        setSearchParams({keyword});
    }

    return <HomePage defaultKeyword={keyword} keywordChange={changeSearchParams} />
}

class HomePage extends React.Component{
    constructor(props){
        super(props);

        this.state = {
            notes: getActiveNotes(),
            keyword: props.defaultKeyword || '',
        }

        // this.onDeleteHandler = this.onDeleteHandler.bind(this);
        this.onKeywordChangeHandler = this.onKeywordChangeHandler.bind(this);
    }

   /* onDeleteHandler(id){
        deleteNote(id);

        this.setState(()=>{
            return {
                notes: getActiveNotes(),
            }
        });
    }
*/
    onKeywordChangeHandler(keyword){
        this.setState(()=>{
            return {
                keyword,
            }
        });

        this.props.keywordChange(keyword);
    }

    render(){
        const notes = this.state.notes.filter((note)=>{
            return note.title.toLowerCase().includes(
                this.state.keyword.toLowerCase()
            );
        });

        console.log('HomePage render - notes:', notes);
        console.log('HomePage render - keyword:', this.state.keyword);

        return (
            <section>
                <h1>TEST - HomePage is rendering</h1>
                <SearchBar keyword={this.state.keyword} keywordChange={this.onKeywordChangeHandler} />
                <h2>Daftar Catatan</h2>
                <NotesList notes={notes} /* onDelete={this.onDeleteHandler} */ />
            </section>
        )
    }
}

export default HomePageWrapper;