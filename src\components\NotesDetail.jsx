import React from 'react';
import PropTypes from 'prop-types';
import NotesItemDate from './NotesItemDate';
import DeleteButton from './DeleteButton';

function NotesDetail({ id, title, createdAt, body, onDelete }){
    return (
        <div className='detail-page'>
            <h2 className='detail-page__title'>{title}</h2>
            <p className='detail-page__createdAt'>
                <NotesItemDate createdAt={createdAt} />
            </p>
            <p className='detail-page__body'>{body}</p>

            <DeleteButton id={id} onDelete={onDelete} />
        </div>
    );
}

NotesDetail.propTypes = {
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    body: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired,
    onDelete: PropTypes.func.isRequired
};

export default NotesDetail;