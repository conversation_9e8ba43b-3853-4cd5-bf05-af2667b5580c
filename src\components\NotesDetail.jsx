import React from 'react';
import PropTypes from 'prop-types';

function NotesDetail({ title, createdAt, body }){
    const formatDate = (dateString) => {
        const date = new Date(dateString);

        const days = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'];
        const months = [
            '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON>', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];

        const dayName = days[date.getDay()];
        const day = date.getDate();
        const monthName = months[date.getMonth()];
        const year = date.getFullYear();

        return `${dayName}, ${day} ${monthName} ${year}`;
    };

    return (
        <div className='detail-page'>
            <h2 className='.detail-page__title'>{title}</h2>
            <p className='detail-page__createdAt'>{formatDate(createdAt)}</p>
            <p className='detail-page__body'>{body}</p>
        </div>
    );
}

NotesDetail.propTypes = {
    title: PropTypes.string.isRequired,
    body: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired
};

export default NotesDetail;