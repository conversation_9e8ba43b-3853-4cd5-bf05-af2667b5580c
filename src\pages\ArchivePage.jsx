import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { getArchivedNotes } from '../utils/local-data';
import NotesList from '../components/NotesList';
import SearchBar from '../components/SearchBar';

function ArchivePageWrapper(){
    const [searchParams, setSearchParams] = useSearchParams();
    const keyword = searchParams.get('keyword');

    function changeSearchParams(keyword){
        setSearchParams({keyword});
    }

    return <ArchivePage defaultKeyword={keyword} keywordChange={changeSearchParams} />
}

class ArchivePage extends React.Component{
    constructor(props){
        super(props);

        this.state = {
            notes: getArchivedNotes(),
            keyword: props.defaultKeyword || '',
        }

        this.onKeywordChangeHandler = this.onKeywordChangeHandler.bind(this);
        this.onArchiveHandler = this.onArchiveHandler.bind(this);
    }


    onKeywordChangeHandler(keyword){
        this.setState(()=>{
            return {
                keyword,
            }
        });

        this.props.keywordChange(keyword);
    }

    onArchiveHandler(id){
        const notes = this.state.notes.map(note=>
            note.id === id ? {...note, archived: !note.archived } : note
        );
        this.setState({notes});
    }

    render(){
    
        const notes = this.state.notes.filter((note)=>{
            return note.title.toLowerCase().includes(
                this.state.keyword.toLowerCase()
            );
        });

        return (
            <section>
                <SearchBar keyword={this.state.keyword} keywordChange={this.onKeywordChangeHandler} />
                <h2>Catatan Arsip</h2> 
                <NotesList notes={notes} onArchive={this.onArchiveHandler} />
            </section>
        )
    }
}

export default ArchivePageWrapper;