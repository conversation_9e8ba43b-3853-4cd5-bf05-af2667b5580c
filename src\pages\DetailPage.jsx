import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import NotesDetail from '../components/NotesDetail';
import { getNote, deleteNote } from '../utils/local-data';

function DetailPageWrapper() {
  const { id } = useParams();
  const navigate = useNavigate();

  const onDeleteHandler = (noteId) => {
    deleteNote(noteId);
    navigate('/');
  };

  return <DetailPage id={String(id)} onDelete={onDeleteHandler} />;
}

class DetailPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      note: getNote(props.id)
    };
  }

  render() {
    if (this.state.note === null) {
      return <p>Catatan tidak ditemukan!</p>;
    }

    return (
      <section>
        <NotesDetail {...this.state.note} onDelete={this.props.onDelete} />
      </section>
    );
  }
}

export default DetailPageWrapper;
