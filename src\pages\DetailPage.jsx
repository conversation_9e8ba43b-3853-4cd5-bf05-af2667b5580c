/* import React from 'react';
import { getNote } from '../utils/local-data';
import { useNavigate } from 'react-router-dom'; */

import React from 'react';
import { useParams } from 'react-router-dom';
import NotesDetail from '../components/NotesDetail';
import { getNote } from '../utils/local-data';

function DetailPageWrapper() {
  const { id } = useParams();
  return <DetailPage id={String(id)} />;
}

class DetailPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      note: getNote(props.id)
    };
  }

  render() {
    if (this.state.note === null) {
      return <p>Catatan tidak ditemukan!</p>;
    }

    return (
      <section>
        <NotesDetail {...this.state.note} />
      </section>
    );
  }
}

export default DetailPageWrapper;
