import React from 'react';
import PropTypes
import { Link, useNavigate } from 'react-router-dom';
import RegisterInput from '../components/RegisterInput';
import { register } from '../utils/network-data';

function RegisterPage(){
    const navigate = useNavigate();

    async function onR<PERSON><PERSON><PERSON><PERSON><PERSON>(user){
        const { error } = await register(user);

        if (!error){
            navigate('/');
        }
    }

    return (
        <section className='register-page'>
            <h2>Register</h2>
            <RegisterInput register={onRegisterHandler} />
            <p>Ke<PERSON><PERSON> ke <Link to='/'>Masuk</Link></p>
        </section>
    )
}

RegisterPage.propTypes = {
    loginSuccess: PropTypes.func.isRequired,
}

export default RegisterPage;