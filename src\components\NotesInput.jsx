import React from 'react';
import PropTypes from 'prop-types';

class NotesInput extends React.Component{
    constructor(props){
        super(props);

        this.state = {
            title: '',
            body: '',
        }

        this.onTitleChangeEventHandler = this.onTitleChangeEventHandler.bind(this);
        this.onBodyChangeEventHandler = this.onBodyChangeEventHandler.bind(this);
        this.onSubmitChangeEventHandler = this.onSubmitChangeEventHandler.bind(this);
    }
    
    onTitleChangeEventHandler(event){
        this.setState(()=>{
            return {
                title: event.target.value,
            }
        });
    }

    onBodyChangeEventHandler(event){
        this.setState(()=>{
            return {
                body: event.target.value,
            }
        });
    }

    onSubmitChangeEventHandler(event){
        event.preventDefault();
        this.props.addNote(this.state);
        this.setState({title: '', body: ''}); 
    }

    render(){
        return (
            <form className='note-input'>
                <h3 className="note-input__title">Buat Catatan</h3>
                <input 
                    type="text" 
                    placeholder="Ini adalah judul..." 
                    value={this.state.title} 
                    onChange={this.onTitleChangeEventHandler}
                />
                <textarea 
                    placeholder="Tuliskan catatanmu di sini..." 
                    value={this.state.body} 
                    onChange={this.onBodyChangeEventHandler}>    
                </textarea>
                <button type="submit" onClick={this.onSubmitChangeEventHandler}>Buat</button>
            </form>
        );
    }
}

NotesInput.propTypes = {
    addNote: PropTypes.func.isRequired,
}

export default NotesInput;
