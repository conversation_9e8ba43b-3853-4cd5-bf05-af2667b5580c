import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FiArchive, FiPlusCircle } from 'react-icons/fi';
import { ThemeConsumer } from '../contexts/ThemeContext';

function Navigation({ logout, name }){
    return (
        <ThemeConsumer>
            {
                ({ theme, toggleTheme }) => {
                    return (
                        <nav className='navigation'>
                            <ul>
                                <li><Link to='/'>Aplikasi Catatan</Link></li>
                                <li><button onClick={toggleTheme}>{theme === 'dark' ? 'light' : 'dark'}</button></li>
                                <li><Link to='/add'><FiPlusCircle/></Link></li>
                                <li><Link to='/archives'><FiArchive/></Link></li>
                            </ul>
                        </nav>
                    );
                }
            }
        </ThemeConsumer>
    );
}

Navigation.propTypes = {
    logout: PropTypes.func.isRequired,
    name: PropTypes.string.isRequired,
}

export default Navigation;