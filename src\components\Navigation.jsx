import React from 'react';
import { Link } from 'react-router-dom';
import { FiArchive, FiPlusCircle, FiLogOut } from 'react-icons/fi';
import { ThemeConsumer } from '../contexts/ThemeContext';
import ToggleTheme from './ToggleTheme';
import PropTypes from 'prop-types';

function Navigation({ logout, name }){
    return (
        <ThemeConsumer>
            {
                ({ theme, toggleTheme }) => {
                    return (
                        <nav className='navigation'>
                            <ul>
                                <li><ToggleTheme /></li>
                                <li><Link to='/'>Aplikasi Catatan</Link></li>
                                <li><Link to='/add'><FiPlusCircle/></Link></li>
                                <li><Link to='/archives'><FiArchive/></Link></li>
                                <li><button className='button-logout' onClick={logout}>{name} <FiLogOut/></button></li>
                            </ul>
                        </nav>
                    );
                }
            }
        </ThemeConsumer>
    );
}

Navigation.propTypes = {
    logout: PropTypes.func.isRequired,
    name: PropTypes.string.isRequired,
}

export default Navigation;