import React from 'react';
import NotesItem from './NotesItem';

function NotesList({notes}){
    if(NotesItem.length === 0){
        return <p className='notes-list-empty'>Tidak ada catatan</p>
    }

    return (
        <div className='notes-list'>
            {
                notes.map((note) => (
                    <NotesItem
                    key={note.id}
                    id={note.id} 
                    {...note}/>
                ))
            }
        </div>
    );
}

export default NotesList;