Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF97F0) msys-2.0.dll+0x1FEBA
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210285FF9, 0007FFFFA7A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA8F0  0002100690B4 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFABD0  00021006A49D (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9F99B0000 ntdll.dll
7FF9F8F10000 KERNEL32.DLL
7FF9F6AA0000 KERNELBASE.dll
7FF9F7640000 USER32.dll
7FF9F7240000 win32u.dll
7FF9F7C00000 GDI32.dll
7FF9F7270000 gdi32full.dll
7FF9F70E0000 msvcp_win.dll
7FF9F6FC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9F7800000 advapi32.dll
7FF9F7CC0000 msvcrt.dll
7FF9F98C0000 sechost.dll
7FF9F7590000 bcrypt.dll
7FF9F78C0000 RPCRT4.dll
7FF9F61D0000 CRYPTBASE.DLL
7FF9F7510000 bcryptPrimitives.dll
7FF9F9210000 IMM32.DLL
