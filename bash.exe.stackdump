Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF218B0000 ntdll.dll
7FFF20E00000 KERNEL32.DLL
7FFF1F100000 KERNELBASE.dll
7FFF1FC20000 USER32.dll
7FFF1F510000 win32u.dll
7FFF21030000 GDI32.dll
7FFF1EEB0000 gdi32full.dll
7FFF1EC10000 msvcp_win.dll
7FFF1EFE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF21250000 advapi32.dll
7FFF1FE80000 msvcrt.dll
7FFF20F80000 sechost.dll
7FFF1F4E0000 bcrypt.dll
7FFF1F9F0000 RPCRT4.dll
7FFF1E0D0000 CRYPTBASE.DLL
7FFF1EB90000 bcryptPrimitives.dll
7FFF20ED0000 IMM32.DLL
