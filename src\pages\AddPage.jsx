import React from 'react';
import { addNote } from '../utils/local-data';
import NotesInput from '../components/NotesInput';
import { useNavigate } from 'react-router-dom';

function AddPage(){
    const navigate = useNavigate();

    function onAddNoteHandler(note){
        addNote(note);
        navigate('/');
    }

    return (
        <main>
            <NotesInput addNote={onAddNoteHandler}/>
        </main>
    );
}

export default AddPage;