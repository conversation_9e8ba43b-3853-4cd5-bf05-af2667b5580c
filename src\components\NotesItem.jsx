import React from 'react';
import NotesItemTitle from './NotesItemTitle';
import NotesItemDate from './NotesItemDate';
import NotesItemBody from './NotesItemBody';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';

function NotesItem({ id, title, createdAt, body }){
    return (
        <div className='note-item'>
            <Link to={`/notes/${id}`}><NotesItemTitle title={title} id={id} /></Link>
            <NotesItemDate createdAt={createdAt} />
            <NotesItemBody body={body}/>
        </div>
    );
}

NotesItem.propTypes = {
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    body: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired
};

export default NotesItem;