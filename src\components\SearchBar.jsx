import React from 'react';
import PropTypes from 'prop-types';

function SearchBar({ keyword, keywordChange }){
    return (
        <div className='search-bar'>
            <h2>Catatan Aktif</h2>
            <input
                className='search-bar'
                type='text'
                placeholder='Cari berda<PERSON> judul'
                value={keyword}  
                onChange={(event)=> keywordChange(event.target.value)} 
            />
        </div>
    );
}

SearchBar.propTypes = {
    keyword: PropTypes.string.isRequired,
    keywordChange: PropTypes.func.isRequired
}

export default SearchBar;