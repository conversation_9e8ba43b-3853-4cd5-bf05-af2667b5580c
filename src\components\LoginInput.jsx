import React from 'react';
import PropTypes from 'prop-types';
import useInput from '../hooks/useInput';

function LoginInput({ login }){
    const [email, onEmailChange] = useInput('');
    const [password, onPasswordChange] = useInput('');

    function onSubmitHandler(event){
        event.preventDefault();

        login({
            email,
            password,
        });
    }

    return (
            <form onSubmit={onSubmitHandler} className='login-input' >
                <input type="text" placeholder="Email" value={email} onChange={onEmailChange} />
                <input type="password" placeholder="Password" value={password} onChange={onPasswordChange} />
                <button type="submit" onClick={onSubmitHandler}>Login</button>
            </form>
        );
}

LoginInput.propTypes = {
    login: PropTypes.func.isRequired,
}

export default LoginInput;