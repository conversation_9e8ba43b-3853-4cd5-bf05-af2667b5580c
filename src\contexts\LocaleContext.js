/* new item
    - locale context
    - theme (light & dark)
    - login page (login input component)
    - register page (register input component)
    - network data (utils)
    - using hooks

    optional 
    - language

*/

import React from 'react';

const LocaleContext = React.createContext();

export const LocaleProvider = LocaleContext.Provider;
export const LocaleConsumer = LocaleContext.Consumer;

export default LocaleContext;

